"use strict";
// File: game-state-server/src/db.ts
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameDatabase = void 0;
var better_sqlite3_1 = require("better-sqlite3");
var fs_1 = require("fs");
var path_1 = require("path");
var os_1 = require("os");
var antagonists_js_1 = require("./antagonists.js");
var health_tracker_js_1 = require("./health-tracker.js");
// Create data directory in user's home folder
var DATA_DIR = (0, path_1.join)((0, os_1.homedir)(), '.rpg-dungeon-data');
if (!(0, fs_1.existsSync)(DATA_DIR)) {
    (0, fs_1.mkdirSync)(DATA_DIR, { recursive: true });
}
var DB_PATH = (0, path_1.join)(DATA_DIR, 'game-state.db');
var GameDatabase = /** @class */ (function () {
    function GameDatabase() {
        this.db = new better_sqlite3_1.default(DB_PATH);
        this.db.pragma('journal_mode = WAL');
        this.initializeSchema();
    }
    GameDatabase.prototype.initializeSchema = function () {
        // --- oWoD-centric Core Character Table ---
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS characters (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL UNIQUE,\n        concept TEXT,\n        game_line TEXT NOT NULL,\n        -- Core Attributes\n        strength INTEGER DEFAULT 1, dexterity INTEGER DEFAULT 1, stamina INTEGER DEFAULT 1,\n        charisma INTEGER DEFAULT 1, manipulation INTEGER DEFAULT 1, appearance INTEGER DEFAULT 1,\n        perception INTEGER DEFAULT 1, intelligence INTEGER DEFAULT 1, wits INTEGER DEFAULT 1,\n        -- Core Traits\n        willpower_current INTEGER DEFAULT 1,\n        willpower_permanent INTEGER DEFAULT 1,\n        health_levels TEXT NOT NULL, -- JSON\n        experience INTEGER DEFAULT 0\n      );\n    ");
        // --- Migration: Add 'experience' column if missing ---
        var cols = this.db.prepare("PRAGMA table_info(characters)").all();
        if (!cols.some(function (c) { return c.name === "experience"; })) {
            this.db.exec("ALTER TABLE characters ADD COLUMN experience INTEGER DEFAULT 0");
        }
        // --- Relational Tables ---
        this.db.exec("CREATE TABLE IF NOT EXISTS character_abilities (character_id INTEGER, ability_name TEXT, ability_type TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, ability_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);");
        this.db.exec("CREATE TABLE IF NOT EXISTS character_disciplines (character_id INTEGER, discipline_name TEXT, rating INTEGER, PRIMARY KEY(character_id, discipline_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);");
        this.db.exec("CREATE TABLE IF NOT EXISTS character_arts (character_id INTEGER, art_name TEXT, rating INTEGER, PRIMARY KEY(character_id, art_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);");
        this.db.exec("CREATE TABLE IF NOT EXISTS character_realms (character_id INTEGER, realm_name TEXT, rating INTEGER, PRIMARY KEY(character_id, realm_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);");
        this.db.exec("CREATE TABLE IF NOT EXISTS character_gifts (character_id INTEGER, gift_name TEXT, rank INTEGER, PRIMARY KEY(character_id, gift_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);");
        this.db.exec("CREATE TABLE IF NOT EXISTS character_spheres (character_id INTEGER, sphere_name TEXT, rating INTEGER, PRIMARY KEY(character_id, sphere_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);");
        this.db.exec("CREATE TABLE IF NOT EXISTS character_derangements (id INTEGER PRIMARY KEY, character_id INTEGER, derangement TEXT, description TEXT, UNIQUE(character_id, derangement), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);");
        this.db.exec("CREATE TABLE IF NOT EXISTS xp_ledger (id INTEGER PRIMARY KEY, character_id INTEGER, type TEXT, amount INTEGER, reason TEXT, trait TEXT, before_xp INTEGER, after_xp INTEGER, created_at DATETIME DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);");
        // --- Inventory Table ---
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS inventory (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        character_id INTEGER NOT NULL,\n        item_name TEXT NOT NULL,\n        item_type TEXT, -- e.g., 'Weapon', 'Trinket', 'Consumable'\n        quantity INTEGER DEFAULT 1,\n        description TEXT,\n        properties TEXT, -- JSON for stats like weapon damage, etc.\n        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE\n      );\n    ");
        // --- World & Story Persistence Tables ---
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS world_state (\n        id INTEGER PRIMARY KEY, -- Use a single row for the whole campaign for simplicity\n        location TEXT,\n        notes TEXT,\n        data TEXT, -- Flexible JSON blob for NPCs, events, etc.\n        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP\n      );\n    ");
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS story_progress (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        chapter INTEGER,\n        scene TEXT,\n        summary TEXT,\n        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP\n      );\n    ");
        // --- Game-line Specific Trait Tables (modular) ---
        // Vampire
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS character_vampire_traits (\n        character_id INTEGER PRIMARY KEY,\n        clan TEXT,\n        generation INTEGER,\n        blood_pool_current INTEGER,\n        blood_pool_max INTEGER,\n        humanity INTEGER,\n        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE\n      );");
        // Werewolf
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS character_werewolf_traits (\n        character_id INTEGER PRIMARY KEY,\n        breed TEXT,\n        auspice TEXT,\n        tribe TEXT,\n        gnosis_current INTEGER,\n        gnosis_permanent INTEGER,\n        rage_current INTEGER,\n        rage_permanent INTEGER,\n        renown_glory INTEGER,\n        renown_honor INTEGER,\n        renown_wisdom INTEGER,\n        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE\n      );");
        // Mage
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS character_mage_traits (\n        character_id INTEGER PRIMARY KEY,\n        tradition_convention TEXT,\n        arete INTEGER,\n        quintessence INTEGER,\n        paradox INTEGER,\n        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE\n      );");
        // Changeling
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS character_changeling_traits (\n        character_id INTEGER PRIMARY KEY,\n        kith TEXT,\n        seeming TEXT,\n        glamour_current INTEGER,\n        glamour_permanent INTEGER,\n        banality_permanent INTEGER,\n        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE\n      );");
        // ADDITION: Experience Ledger table for character XP transactions
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS experience_ledger (\n        id INTEGER PRIMARY KEY,\n        character_id INTEGER NOT NULL,\n        amount INTEGER NOT NULL,\n        reason TEXT,\n        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE\n      );\n    ");
        // --- Refactored Modular Antagonists/NPCs Table ---
        this.db.exec("DROP TABLE IF EXISTS npcs;"); // Backup data first!
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS npcs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL,\n        template TEXT,\n        concept TEXT,\n        game_line TEXT NOT NULL,\n        strength INTEGER DEFAULT 1, dexterity INTEGER DEFAULT 1, stamina INTEGER DEFAULT 1,\n        charisma INTEGER DEFAULT 1, manipulation INTEGER DEFAULT 1, appearance INTEGER DEFAULT 1,\n        perception INTEGER DEFAULT 1, intelligence INTEGER DEFAULT 1, wits INTEGER DEFAULT 1,\n        willpower_current INTEGER DEFAULT 1,\n        willpower_permanent INTEGER DEFAULT 1,\n        health_levels TEXT NOT NULL, -- JSON\n        notes TEXT\n      );\n    ");
        // Modular splat trait tables for NPCs -- structure mirrors player traits
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS npc_vampire_traits (\n        npc_id INTEGER PRIMARY KEY,\n        clan TEXT,\n        generation INTEGER,\n        blood_pool_current INTEGER,\n        blood_pool_max INTEGER,\n        humanity INTEGER,\n        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE\n      );");
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS npc_werewolf_traits (\n        npc_id INTEGER PRIMARY KEY,\n        breed TEXT,\n        auspice TEXT,\n        tribe TEXT,\n        gnosis_current INTEGER,\n        gnosis_permanent INTEGER,\n        rage_current INTEGER,\n        rage_permanent INTEGER,\n        renown_glory INTEGER,\n        renown_honor INTEGER,\n        renown_wisdom INTEGER,\n        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE\n      );");
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS npc_mage_traits (\n        npc_id INTEGER PRIMARY KEY,\n        tradition_convention TEXT,\n        arete INTEGER,\n        quintessence INTEGER,\n        paradox INTEGER,\n        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE\n      );");
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS npc_changeling_traits (\n        npc_id INTEGER PRIMARY KEY,\n        kith TEXT,\n        seeming TEXT,\n        glamour_current INTEGER,\n        glamour_permanent INTEGER,\n        banality_permanent INTEGER,\n        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE\n      );");
        // --- Initiative Tracking Table ---
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS initiative_order (\n        scene_id TEXT NOT NULL,\n        character_id INTEGER,\n        npc_id INTEGER,\n        actor_name TEXT NOT NULL,\n        initiative_score INTEGER NOT NULL,\n        turn_order INTEGER NOT NULL,\n        PRIMARY KEY(scene_id, turn_order),\n        FOREIGN KEY(character_id) REFERENCES characters(id) ON DELETE SET NULL,\n        FOREIGN KEY(npc_id) REFERENCES npcs(id) ON DELETE SET NULL\n      );\n    ");
        // --- Turn Management Table for Combat Scenes ---
        this.db.exec("\n        CREATE TABLE IF NOT EXISTS scenes (\n          scene_id TEXT PRIMARY KEY,\n          current_round INTEGER NOT NULL DEFAULT 1,\n          current_turn_order INTEGER NOT NULL DEFAULT 0\n        );\n      ");
        // --- Generic Status Effects Table ---
        this.db.exec("\n      CREATE TABLE IF NOT EXISTS status_effects (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        character_id INTEGER,\n        npc_id INTEGER,\n        effect_name TEXT NOT NULL,\n        description TEXT,\n        mechanical_effect TEXT,\n        duration_type TEXT DEFAULT 'indefinite',\n        duration_value INTEGER,\n        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE,\n        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE\n      );\n    ");
    };
    GameDatabase.prototype.createCharacter = function (data) {
        var _this = this;
        var health_levels = data.health_levels || { bruised: 0, hurt: 0, injured: 0, wounded: 0, mauled: 0, crippled: 0, incapacitated: 0 };
        var charId = undefined;
        // Transactional logic: all sub-table inserts are done atomically
        charId = this.db.transaction(function () {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6;
            var localCharId;
            // Insert core character data
            var stmt = _this.db.prepare("\n        INSERT INTO characters (\n          name, concept, game_line,\n          strength, dexterity, stamina, charisma, manipulation, appearance,\n          perception, intelligence, wits,\n          willpower_current, willpower_permanent, health_levels, experience\n        ) VALUES (\n          @name, @concept, @game_line,\n          @strength, @dexterity, @stamina, @charisma, @manipulation, @appearance,\n          @perception, @intelligence, @wits,\n          @willpower_current, @willpower_permanent, @health_levels, @experience\n        )\n      ");
            var result = stmt.run(__assign(__assign({}, data), { health_levels: JSON.stringify(health_levels), experience: data.experience || 0 }));
            localCharId = result.lastInsertRowid;
            // --- Insert into game-line-specific tables ---
            switch (data.game_line) {
                case 'vampire':
                    _this.db.prepare("\n            INSERT INTO character_vampire_traits\n            (character_id, clan, generation, blood_pool_current, blood_pool_max, humanity)\n            VALUES (?, ?, ?, ?, ?, ?)\n          ").run(localCharId, (_a = data.clan) !== null && _a !== void 0 ? _a : null, (_b = data.generation) !== null && _b !== void 0 ? _b : null, (_c = data.blood_pool_current) !== null && _c !== void 0 ? _c : null, (_d = data.blood_pool_max) !== null && _d !== void 0 ? _d : null, (_e = data.humanity) !== null && _e !== void 0 ? _e : null);
                    break;
                case 'werewolf':
                    _this.db.prepare("\n            INSERT INTO character_werewolf_traits\n            (character_id, breed, auspice, tribe, gnosis_current, gnosis_permanent, rage_current, rage_permanent, renown_glory, renown_honor, renown_wisdom)\n            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n          ").run(localCharId, (_f = data.breed) !== null && _f !== void 0 ? _f : null, (_g = data.auspice) !== null && _g !== void 0 ? _g : null, (_h = data.tribe) !== null && _h !== void 0 ? _h : null, (_j = data.gnosis_current) !== null && _j !== void 0 ? _j : null, (_k = data.gnosis_permanent) !== null && _k !== void 0 ? _k : null, (_l = data.rage_current) !== null && _l !== void 0 ? _l : null, (_m = data.rage_permanent) !== null && _m !== void 0 ? _m : null, (_o = data.renown_glory) !== null && _o !== void 0 ? _o : null, (_p = data.renown_honor) !== null && _p !== void 0 ? _p : null, (_q = data.renown_wisdom) !== null && _q !== void 0 ? _q : null);
                    break;
                case 'mage':
                    _this.db.prepare("\n            INSERT INTO character_mage_traits\n            (character_id, tradition_convention, arete, quintessence, paradox)\n            VALUES (?, ?, ?, ?, ?)\n          ").run(localCharId, (_r = data.tradition_convention) !== null && _r !== void 0 ? _r : null, (_s = data.arete) !== null && _s !== void 0 ? _s : null, (_t = data.quintessence) !== null && _t !== void 0 ? _t : null, (_u = data.paradox) !== null && _u !== void 0 ? _u : null);
                    break;
                case 'changeling':
                    _this.db.prepare("\n            INSERT INTO character_changeling_traits\n            (character_id, kith, seeming, glamour_current, glamour_permanent, banality_permanent)\n            VALUES (?, ?, ?, ?, ?, ?)\n          ").run(localCharId, (_v = data.kith) !== null && _v !== void 0 ? _v : null, (_w = data.seeming) !== null && _w !== void 0 ? _w : null, (_x = data.glamour_current) !== null && _x !== void 0 ? _x : null, (_y = data.glamour_permanent) !== null && _y !== void 0 ? _y : null, (_z = data.banality_permanent) !== null && _z !== void 0 ? _z : null);
                    break;
                // Additional splats can be added here in similar fashion
            }
            // Changeling-specific: arts/reals
            if (data.game_line === "changeling") {
                if (data.arts && Array.isArray(data.arts)) {
                    var artStmt = _this.db.prepare("INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)");
                    for (var _i = 0, _7 = data.arts; _i < _7.length; _i++) {
                        var a = _7[_i];
                        artStmt.run(localCharId, (_2 = (_1 = (_0 = a.art_name) !== null && _0 !== void 0 ? _0 : a.name) !== null && _1 !== void 0 ? _1 : a.label) !== null && _2 !== void 0 ? _2 : '', Number(a.rating) || 0);
                    }
                }
                if (data.realms && Array.isArray(data.realms)) {
                    var realmStmt = _this.db.prepare("INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)");
                    for (var _8 = 0, _9 = data.realms; _8 < _9.length; _8++) {
                        var r = _9[_8];
                        realmStmt.run(localCharId, (_5 = (_4 = (_3 = r.realm_name) !== null && _3 !== void 0 ? _3 : r.name) !== null && _4 !== void 0 ? _4 : r.label) !== null && _5 !== void 0 ? _5 : '', Number(r.rating) || 0);
                    }
                }
            }
            // Example sub-table transactional inserts; expand for all relations as needed
            if (data.abilities && Array.isArray(data.abilities)) {
                var abilityStmt = _this.db.prepare("INSERT INTO character_abilities (character_id, ability_name, ability_type, rating, specialty)\n           VALUES (?, ?, ?, ?, ?)");
                for (var _10 = 0, _11 = data.abilities; _10 < _11.length; _10++) {
                    var ability = _11[_10];
                    abilityStmt.run(localCharId, ability.name, ability.type, ability.rating, (_6 = ability.specialty) !== null && _6 !== void 0 ? _6 : null);
                }
            }
            if (data.disciplines && Array.isArray(data.disciplines)) {
                var discStmt = _this.db.prepare("INSERT INTO character_disciplines (character_id, discipline_name, rating)\n           VALUES (?, ?, ?)");
                for (var _12 = 0, _13 = data.disciplines; _12 < _13.length; _12++) {
                    var d = _13[_12];
                    discStmt.run(localCharId, d.name, d.rating);
                }
            }
            // ... perform additional transactional inserts for arts, realms, gifts, etc., as needed
            return localCharId;
        })();
        return this.getCharacter(charId);
    };
    GameDatabase.prototype.createAntagonist = function (template_name, custom_name) {
        var _this = this;
        var template = antagonists_js_1.ANTAGONIST_TEMPLATES[template_name];
        if (!template)
            return null;
        var data = __assign(__assign({}, template), { name: custom_name || template.name || template_name, template: template_name });
        var npcId = undefined;
        this.db.transaction(function () {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5;
            // 1. Insert into new lean core npcs table (no game-line-specific splat traits here)
            var stmt = _this.db.prepare("\n        INSERT INTO npcs (\n          name, template, concept, game_line,\n          strength, dexterity, stamina, charisma, manipulation, appearance,\n          perception, intelligence, wits,\n          willpower_current, willpower_permanent, health_levels, notes\n        ) VALUES (\n          @name, @template, @concept, @game_line,\n          @strength, @dexterity, @stamina, @charisma, @manipulation, @appearance,\n          @perception, @intelligence, @wits,\n          @willpower_current, @willpower_permanent, @health_levels, @notes\n        )\n      ");
            var result = stmt.run(__assign(__assign({}, data), { health_levels: JSON.stringify((_a = data.health_levels) !== null && _a !== void 0 ? _a : {}) }));
            npcId = result.lastInsertRowid;
            // 2. Populate game-line-specific traits in new modular tables
            switch (template.game_line) {
                case 'vampire':
                    _this.db.prepare("\n            INSERT INTO npc_vampire_traits\n            (npc_id, clan, generation, blood_pool_current, blood_pool_max, humanity)\n            VALUES (?, ?, ?, ?, ?, ?)\n          ").run(npcId, (_b = template.clan) !== null && _b !== void 0 ? _b : null, (_c = template.generation) !== null && _c !== void 0 ? _c : null, (_d = template.blood_pool_current) !== null && _d !== void 0 ? _d : null, (_e = template.blood_pool_max) !== null && _e !== void 0 ? _e : null, (_f = template.humanity) !== null && _f !== void 0 ? _f : null);
                    break;
                case 'werewolf':
                    _this.db.prepare("\n            INSERT INTO npc_werewolf_traits\n            (npc_id, breed, auspice, tribe, gnosis_current, gnosis_permanent, rage_current, rage_permanent, renown_glory, renown_honor, renown_wisdom)\n            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n          ").run(npcId, (_g = template.breed) !== null && _g !== void 0 ? _g : null, (_h = template.auspice) !== null && _h !== void 0 ? _h : null, (_j = template.tribe) !== null && _j !== void 0 ? _j : null, (_k = template.gnosis_current) !== null && _k !== void 0 ? _k : null, (_l = template.gnosis_permanent) !== null && _l !== void 0 ? _l : null, (_m = template.rage_current) !== null && _m !== void 0 ? _m : null, (_o = template.rage_permanent) !== null && _o !== void 0 ? _o : null, (_p = template.renown_glory) !== null && _p !== void 0 ? _p : null, (_q = template.renown_honor) !== null && _q !== void 0 ? _q : null, (_r = template.renown_wisdom) !== null && _r !== void 0 ? _r : null);
                    break;
                case 'mage':
                    _this.db.prepare("\n            INSERT INTO npc_mage_traits\n            (npc_id, tradition_convention, arete, quintessence, paradox)\n            VALUES (?, ?, ?, ?, ?)\n          ").run(npcId, (_s = template.tradition_convention) !== null && _s !== void 0 ? _s : null, (_t = template.arete) !== null && _t !== void 0 ? _t : null, (_u = template.quintessence) !== null && _u !== void 0 ? _u : null, (_v = template.paradox) !== null && _v !== void 0 ? _v : null);
                    break;
                case 'changeling':
                    _this.db.prepare("\n            INSERT INTO npc_changeling_traits\n            (npc_id, kith, seeming, glamour_current, glamour_permanent, banality_permanent)\n            VALUES (?, ?, ?, ?, ?, ?)\n          ").run(npcId, (_w = template.kith) !== null && _w !== void 0 ? _w : null, (_x = template.seeming) !== null && _x !== void 0 ? _x : null, (_y = template.glamour_current) !== null && _y !== void 0 ? _y : null, (_z = template.glamour_permanent) !== null && _z !== void 0 ? _z : null, (_0 = template.banality_permanent) !== null && _0 !== void 0 ? _0 : null);
                    break;
                // Expand for other splats as needed
            }
            // 3. Relational data (abilities, disciplines, gifts, spheres, arts, realms)
            if (template.abilities) {
                var abilities = template.abilities;
                var abilityStmt = _this.db.prepare("INSERT INTO character_abilities (character_id, ability_name, ability_type, rating, specialty) VALUES (?, ?, ?, ?, NULL)");
                if (abilities.talents) {
                    for (var _i = 0, _6 = Object.entries(abilities.talents); _i < _6.length; _i++) {
                        var _7 = _6[_i], name_1 = _7[0], rating = _7[1];
                        abilityStmt.run(npcId, name_1, 'Talent', rating);
                    }
                }
                if (abilities.skills) {
                    for (var _8 = 0, _9 = Object.entries(abilities.skills); _8 < _9.length; _8++) {
                        var _10 = _9[_8], name_2 = _10[0], rating = _10[1];
                        abilityStmt.run(npcId, name_2, 'Skill', rating);
                    }
                }
                if (abilities.knowledges) {
                    for (var _11 = 0, _12 = Object.entries(abilities.knowledges); _11 < _12.length; _11++) {
                        var _13 = _12[_11], name_3 = _13[0], rating = _13[1];
                        abilityStmt.run(npcId, name_3, 'Knowledge', rating);
                    }
                }
            }
            if ((_1 = template.supernatural) === null || _1 === void 0 ? void 0 : _1.disciplines) {
                var discStmt = _this.db.prepare("INSERT INTO character_disciplines (character_id, discipline_name, rating) VALUES (?, ?, ?)");
                for (var _14 = 0, _15 = Object.entries(template.supernatural.disciplines); _14 < _15.length; _14++) {
                    var _16 = _15[_14], name_4 = _16[0], rating = _16[1];
                    discStmt.run(npcId, name_4, rating);
                }
            }
            if ((_2 = template.supernatural) === null || _2 === void 0 ? void 0 : _2.gifts) {
                var giftStmt = _this.db.prepare("INSERT INTO character_gifts (character_id, gift_name, rank) VALUES (?, ?, ?)");
                for (var _17 = 0, _18 = Object.entries(template.supernatural.gifts); _17 < _18.length; _17++) {
                    var _19 = _18[_17], name_5 = _19[0], rank = _19[1];
                    giftStmt.run(npcId, name_5, rank);
                }
            }
            if ((_3 = template.supernatural) === null || _3 === void 0 ? void 0 : _3.spheres) {
                var sphStmt = _this.db.prepare("INSERT INTO character_spheres (character_id, sphere_name, rating) VALUES (?, ?, ?)");
                for (var _20 = 0, _21 = Object.entries(template.supernatural.spheres); _20 < _21.length; _20++) {
                    var _22 = _21[_20], name_6 = _22[0], rating = _22[1];
                    sphStmt.run(npcId, name_6, rating);
                }
            }
            if ((_4 = template.supernatural) === null || _4 === void 0 ? void 0 : _4.arts) {
                var artStmt = _this.db.prepare("INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)");
                for (var _23 = 0, _24 = Object.entries(template.supernatural.arts); _23 < _24.length; _23++) {
                    var _25 = _24[_23], name_7 = _25[0], rating = _25[1];
                    artStmt.run(npcId, name_7, rating);
                }
            }
            if ((_5 = template.supernatural) === null || _5 === void 0 ? void 0 : _5.realms) {
                var realmStmt = _this.db.prepare("INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)");
                for (var _26 = 0, _27 = Object.entries(template.supernatural.realms); _26 < _27.length; _26++) {
                    var _28 = _27[_26], name_8 = _28[0], rating = _28[1];
                    realmStmt.run(npcId, name_8, rating);
                }
            }
            // ... Add more relational logic for new splats here if needed
        })();
        return this.getAntagonistById(npcId);
    };
    GameDatabase.prototype.getAntagonistById = function (npc_id) {
        return this.db.prepare('SELECT * FROM npcs WHERE id = ?').get(npc_id);
    };
    GameDatabase.prototype.getCharacter = function (id) {
        var char = this.db.prepare('SELECT * FROM characters WHERE id = ?').get(id);
        if (!char)
            return null;
        var extraData = {};
        switch (char.game_line) {
            case 'vampire':
                extraData = this.db.prepare('SELECT * FROM character_vampire_traits WHERE character_id = ?').get(id) || {};
                break;
            case 'werewolf':
                extraData = this.db.prepare('SELECT * FROM character_werewolf_traits WHERE character_id = ?').get(id) || {};
                break;
            case 'mage':
                extraData = this.db.prepare('SELECT * FROM character_mage_traits WHERE character_id = ?').get(id) || {};
                break;
            case 'changeling':
                extraData = this.db.prepare('SELECT * FROM character_changeling_traits WHERE character_id = ?').get(id) || {};
                break;
            // Add more splats as needed here
        }
        // Fetch all related data
        var abilities = this.db.prepare('SELECT * FROM character_abilities WHERE character_id = ?').all(id);
        var disciplines = this.db.prepare('SELECT * FROM character_disciplines WHERE character_id = ?').all(id);
        var arts = this.db.prepare('SELECT * FROM character_arts WHERE character_id = ?').all(id);
        var realms = this.db.prepare('SELECT * FROM character_realms WHERE character_id = ?').all(id);
        // ... fetch for gifts, spheres ...
        return __assign(__assign(__assign({}, char), extraData), { health_levels: JSON.parse(char.health_levels), abilities: abilities, disciplines: disciplines, arts: arts, realms: realms });
    };
    GameDatabase.prototype.getCharacterByName = function (name) {
        var char = this.db.prepare('SELECT * FROM characters WHERE name = ?').get(name);
        if (!char)
            return null;
        return this.getCharacter(char.id);
    };
    GameDatabase.prototype.updateCharacter = function (id, updates) {
        var _this = this;
        var validKeys = [
            'name', 'concept', 'game_line', 'strength', 'dexterity', 'stamina', 'charisma', 'manipulation', 'appearance', 'perception', 'intelligence', 'wits',
            'willpower_current', 'willpower_permanent', 'health_levels', 'power_stat_name', 'power_stat_rating', 'experience',
            'clan', 'generation', 'blood_pool_current', 'blood_pool_max', 'humanity',
            // ... all other game-line specific scalar fields
        ];
        var scalarFields = Object.keys(updates).filter(function (k) { return validKeys.includes(k); });
        this.db.transaction(function () {
            var _a;
            if (scalarFields.length > 0) {
                var values = scalarFields.map(function (f) { return (f === 'health_levels' && typeof updates[f] === 'object') ? JSON.stringify(updates[f]) : updates[f]; });
                var setClause = scalarFields.map(function (f) { return "`".concat(f, "` = ?"); }).join(', ');
                (_a = _this.db.prepare("UPDATE characters SET ".concat(setClause, " WHERE id = ?"))).run.apply(_a, __spreadArray(__spreadArray([], values, false), [id], false));
            }
            // ... (your logic for updating sub-arrays like abilities, disciplines, etc.)
        })();
        return this.getCharacter(id);
    };
    // STUBBED OUT MISSING METHODS TO ALLOW COMPILATION
    GameDatabase.prototype.getDerangements = function (characterId) { return []; };
    // --- Inventory Management ---
    GameDatabase.prototype.addItem = function (character_id, item) {
        var stmt = this.db.prepare("\n      INSERT INTO inventory (character_id, item_name, item_type, quantity, description, properties)\n      VALUES (?, ?, ?, ?, ?, ?)\n    ");
        var result = stmt.run(character_id, item.name, item.type || 'misc', item.quantity || 1, item.description || null, item.properties ? JSON.stringify(item.properties) : null);
        return __assign({ id: result.lastInsertRowid }, item);
    };
    GameDatabase.prototype.getInventory = function (character_id) {
        var stmt = this.db.prepare('SELECT * FROM inventory WHERE character_id = ?');
        return stmt.all(character_id).map(function (item) { return (__assign(__assign({}, item), { properties: item.properties ? JSON.parse(item.properties) : null })); });
    };
    GameDatabase.prototype.updateItem = function (item_id, updates) {
        var _a;
        var fields = Object.keys(updates);
        var values = fields.map(function (key) {
            var value = updates[key];
            return typeof value === 'object' ? JSON.stringify(value) : value;
        });
        if (fields.length === 0)
            return;
        var setClause = fields.map(function (f) { return "".concat(f, " = ?"); }).join(', ');
        (_a = this.db.prepare("UPDATE inventory SET ".concat(setClause, " WHERE id = ?"))).run.apply(_a, __spreadArray(__spreadArray([], values, false), [item_id], false));
    };
    GameDatabase.prototype.removeItem = function (item_id) {
        var res = this.db.prepare('DELETE FROM inventory WHERE id = ?').run(item_id);
        return res.changes > 0;
    };
    // --- World & Story Persistence ---
    GameDatabase.prototype.saveWorldState = function (state) {
        var dataStr = state.data ? JSON.stringify(state.data) : null;
        // Use UPSERT to either update row 1 or insert it if it doesn't exist.
        this.db.prepare("\n      INSERT INTO world_state (id, location, notes, data, last_updated)\n      VALUES (1, @location, @notes, @data, CURRENT_TIMESTAMP)\n      ON CONFLICT(id) DO UPDATE SET\n        location = excluded.location,\n        notes = excluded.notes,\n        data = excluded.data,\n        last_updated = excluded.last_updated;\n    ").run({ location: state.location, notes: state.notes, data: dataStr });
    };
    GameDatabase.prototype.getWorldState = function () {
        var state = this.db.prepare('SELECT * FROM world_state WHERE id = 1').get();
        if (state && state.data) {
            state.data = JSON.parse(state.data);
        }
        return state;
    };
    GameDatabase.prototype.saveStoryProgress = function (progress) {
        this.db.prepare('INSERT INTO story_progress (chapter, scene, summary) VALUES (?, ?, ?)').run(progress.chapter, progress.scene, progress.summary);
    };
    // --- Antagonist & Character Management ---
    GameDatabase.prototype.updateAntagonist = function (npc_id, updates) {
        var _a;
        // This reuses the same logic as updateCharacter but targets the 'npcs' table
        // and its related modular trait tables. This will require a more complex,
        // game-line-aware update transaction, similar to createAntagonist.
        // For now, a simple core update:
        var scalarFields = Object.keys(updates).filter(function (k) { return !['abilities', 'disciplines'].includes(k); });
        if (scalarFields.length > 0) {
            var setClause = scalarFields.map(function (f) { return "".concat(f, " = ?"); }).join(', ');
            (_a = this.db.prepare("UPDATE npcs SET ".concat(setClause, " WHERE id = ?"))).run.apply(_a, __spreadArray(__spreadArray([], scalarFields.map(function (k) { return updates[k]; }), false), [npc_id], false));
        }
        // TODO: Add logic to update relational tables (abilities, disciplines, etc.)
    };
    GameDatabase.prototype.listAntagonists = function () {
        return this.db.prepare('SELECT id, name, game_line, concept FROM npcs ORDER BY name').all();
    };
    GameDatabase.prototype.removeAntagonist = function (npc_id) {
        return this.db.prepare('DELETE FROM npcs WHERE id = ?').run(npc_id).changes > 0;
    };
    GameDatabase.prototype.listCharacters = function () {
        return this.db.prepare('SELECT id, name, game_line, concept FROM characters ORDER BY name').all();
    };
    GameDatabase.prototype.applyHealthLevelDamage = function (targetType, targetId, damage) {
        // Robust, HealthTracker-based health/damage logic for target (character or npc)
        var table = targetType === 'character' ? 'characters' : 'npcs';
        var rec = this.db.prepare("SELECT id, health_levels FROM ".concat(table, " WHERE id = ?")).get(targetId);
        if (!rec)
            return { success: false, message: "".concat(targetType, " not found") };
        var tracker = health_tracker_js_1.HealthTracker.from(rec.health_levels);
        var changed = tracker.applyDamage(damage);
        // Save back if changed
        if (changed) {
            this.db.prepare("UPDATE ".concat(table, " SET health_levels = ? WHERE id = ?")).run(tracker.serialize(), targetId);
        }
        var boxes = tracker.getBoxArray();
        var penalty = tracker.getWoundPenalty();
        var statusText = "Health: ".concat(boxes.join('|'), " | Penalty: ").concat(penalty);
        return { success: true, newHealth: boxes, woundPenalty: penalty, statusText: statusText };
    };
    // Add an entry to the experience_ledger for XP tracking
    GameDatabase.prototype.addExperienceLedgerEntry = function (character_id, amount, reason) {
        this.db.prepare("INSERT INTO experience_ledger (character_id, amount, reason) VALUES (?, ?, ?)").run(character_id, amount, reason);
    };
    /**
       * Atomically improves a trait for XP-based progression following oWoD rules.
       * @param character_id The character's ID.
       * @param trait_type One of: attribute, ability, discipline, sphere, art, realm, willpower, power_stat
       * @param trait_name Name of trait (column or row name)
       * @param xp_cost  XP cost applied (for validation/deduction)
       * @returns {object} - { new_rating, trait_type, trait_name }
       */
    /**
     * Atomically improves a trait for XP-based progression following oWoD rules.
     * Calculates XP cost and applies rules inside the method.
     * @param character_id The character's ID.
     * @param trait_type One of: attribute, ability, discipline, sphere, art, realm, willpower, power_stat
     * @param trait_name Name of trait (column or row name)
     * @returns {object} - { new_rating, trait_type, trait_name, xp_cost }
     */
    GameDatabase.prototype.improveTrait = function (character_id, trait_type, trait_name) {
        var _this = this;
        var char = this.getCharacter(character_id);
        if (!char)
            throw new Error("Character not found.");
        var curr_rating = 0;
        // Get the current value depending on the trait_type
        switch (trait_type) {
            case 'attribute':
                curr_rating = char[trait_name];
                if (typeof curr_rating !== "number")
                    throw new Error("Attribute '".concat(trait_name, "' not found or invalid."));
                break;
            case 'willpower':
                curr_rating = char['willpower_permanent'];
                if (typeof curr_rating !== "number")
                    throw new Error("Willpower not found.");
                break;
            case 'power_stat':
                if (char.power_stat_name === trait_name) {
                    curr_rating = char.power_stat_rating;
                    if (typeof curr_rating !== 'number')
                        throw new Error("power_stat_rating not found.");
                }
                else {
                    throw new Error("power_stat_name mismatch.");
                }
                break;
            case 'ability': {
                var ab = (char.abilities || []).find(function (a) { var _a; return ((_a = a.ability_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name.toLowerCase(); });
                curr_rating = ab ? ab.rating : 0;
                break;
            }
            case 'discipline': {
                var d = (char.disciplines || []).find(function (a) { var _a; return ((_a = a.discipline_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name.toLowerCase(); });
                curr_rating = d ? d.rating : 0;
                break;
            }
            case 'sphere':
                if (char.spheres) {
                    var sph = (char.spheres || []).find(function (s) { var _a; return ((_a = s.sphere_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name.toLowerCase(); });
                    curr_rating = sph ? sph.rating : 0;
                }
                break;
            case 'art':
                if (char.arts) {
                    var a = (char.arts || []).find(function (a) { var _a; return ((_a = a.art_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name.toLowerCase(); });
                    curr_rating = a ? a.rating : 0;
                }
                break;
            case 'realm':
                if (char.realms) {
                    var r = (char.realms || []).find(function (r) { var _a; return ((_a = r.realm_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name.toLowerCase(); });
                    curr_rating = r ? r.rating : 0;
                }
                break;
            default:
                throw new Error("Unsupported trait_type: ".concat(trait_type));
        }
        var new_rating = curr_rating + 1;
        var xp_cost = 0;
        switch (trait_type) {
            case 'attribute':
                xp_cost = new_rating * 4;
                break;
            case 'ability':
                xp_cost = new_rating * 2;
                break;
            case 'discipline':
                xp_cost = new_rating * 5;
                break;
            case 'sphere':
            case 'art':
            case 'realm':
                xp_cost = new_rating * 7;
                break;
            case 'willpower':
                xp_cost = 8;
                break;
            case 'power_stat':
                xp_cost = new_rating * 8;
                break;
            default: throw new Error("Unsupported trait_type: ".concat(trait_type));
        }
        // Validation: enough XP
        if ((char.experience || 0) < xp_cost) {
            throw new Error("Not enough XP. Has ".concat(char.experience, ", needs ").concat(xp_cost, "."));
        }
        // 2. Upgrade trait logic per type
        this.db.transaction(function () {
            var _a;
            switch (trait_type) {
                case 'attribute': {
                    var col = trait_name;
                    _this.updateCharacter(character_id, (_a = {}, _a[col] = new_rating, _a.experience = char.experience - xp_cost, _a));
                    break;
                }
                case 'willpower': {
                    _this.updateCharacter(character_id, { willpower_permanent: new_rating, experience: char.experience - xp_cost });
                    break;
                }
                case 'power_stat': {
                    _this.updateCharacter(character_id, { power_stat_rating: new_rating, experience: char.experience - xp_cost });
                    break;
                }
                case 'ability': {
                    var existing = _this.db.prepare('SELECT rating FROM character_abilities WHERE character_id = ? AND ability_name = ?').get(character_id, trait_name);
                    if (existing && typeof existing.rating === "number") {
                        _this.db.prepare('UPDATE character_abilities SET rating = ? WHERE character_id = ? AND ability_name = ?').run(new_rating, character_id, trait_name);
                    }
                    else {
                        _this.db.prepare('INSERT INTO character_abilities (character_id, ability_name, ability_type, rating) VALUES (?, ?, ?, ?)').run(character_id, trait_name, '', new_rating);
                    }
                    _this.updateCharacter(character_id, { experience: char.experience - xp_cost });
                    break;
                }
                case 'discipline': {
                    var existing = _this.db.prepare('SELECT rating FROM character_disciplines WHERE character_id = ? AND discipline_name = ?').get(character_id, trait_name);
                    if (existing && typeof existing.rating === "number") {
                        _this.db.prepare('UPDATE character_disciplines SET rating = ? WHERE character_id = ? AND discipline_name = ?').run(new_rating, character_id, trait_name);
                    }
                    else {
                        _this.db.prepare('INSERT INTO character_disciplines (character_id, discipline_name, rating) VALUES (?, ?, ?)').run(character_id, trait_name, new_rating);
                    }
                    _this.updateCharacter(character_id, { experience: char.experience - xp_cost });
                    break;
                }
                case 'sphere': {
                    var existing = _this.db.prepare('SELECT rating FROM character_spheres WHERE character_id = ? AND sphere_name = ?').get(character_id, trait_name);
                    if (existing && typeof existing.rating === "number") {
                        _this.db.prepare('UPDATE character_spheres SET rating = ? WHERE character_id = ? AND sphere_name = ?').run(new_rating, character_id, trait_name);
                    }
                    else {
                        _this.db.prepare('INSERT INTO character_spheres (character_id, sphere_name, rating) VALUES (?, ?, ?)').run(character_id, trait_name, new_rating);
                    }
                    _this.updateCharacter(character_id, { experience: char.experience - xp_cost });
                    break;
                }
                case 'art': {
                    var existing = _this.db.prepare('SELECT rating FROM character_arts WHERE character_id = ? AND art_name = ?').get(character_id, trait_name);
                    if (existing && typeof existing.rating === "number") {
                        _this.db.prepare('UPDATE character_arts SET rating = ? WHERE character_id = ? AND art_name = ?').run(new_rating, character_id, trait_name);
                    }
                    else {
                        _this.db.prepare('INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)').run(character_id, trait_name, new_rating);
                    }
                    _this.updateCharacter(character_id, { experience: char.experience - xp_cost });
                    break;
                }
                case 'realm': {
                    var existing = _this.db.prepare('SELECT rating FROM character_realms WHERE character_id = ? AND realm_name = ?').get(character_id, trait_name);
                    if (existing && typeof existing.rating === "number") {
                        _this.db.prepare('UPDATE character_realms SET rating = ? WHERE character_id = ? AND realm_name = ?').run(new_rating, character_id, trait_name);
                    }
                    else {
                        _this.db.prepare('INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)').run(character_id, trait_name, new_rating);
                    }
                    _this.updateCharacter(character_id, { experience: char.experience - xp_cost });
                    break;
                }
                default:
                    throw new Error("Unsupported trait_type: ".concat(trait_type));
            }
            // XP ledger log
            _this.addExperienceLedgerEntry(character_id, -xp_cost, "[IMPROVE] ".concat(trait_type, ":").concat(trait_name));
        })();
        return { new_rating: new_rating, trait_type: trait_type, trait_name: trait_name, xp_cost: xp_cost };
    };
    /**
     * Set the initiative order for a scene.
     * @param scene_id The ID of the scene.
     * @param entries Array<{character_id, npc_id, actor_name, initiative_score, turn_order}>
     *   character_id or npc_id is required (others can be null), actor_name required.
     *   Overwrites all previous initiative for the scene.
     */
    GameDatabase.prototype.setInitiativeOrder = function (scene_id, entries) {
        var _this = this;
        this.db.transaction(function () {
            var _a, _b;
            _this.db.prepare("DELETE FROM initiative_order WHERE scene_id = ?").run(scene_id);
            var stmt = _this.db.prepare("\n        INSERT INTO initiative_order (scene_id, character_id, npc_id, actor_name, initiative_score, turn_order)\n        VALUES (?, ?, ?, ?, ?, ?)\n      ");
            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {
                var e = entries_1[_i];
                stmt.run(scene_id, (_a = e.character_id) !== null && _a !== void 0 ? _a : null, (_b = e.npc_id) !== null && _b !== void 0 ? _b : null, e.actor_name, e.initiative_score, e.turn_order);
            }
        })();
    };
    /**
     * Get initiative order for a scene.
     * @param scene_id The ID of the scene.
     * @returns Array<{character_id, npc_id, actor_name, initiative_score, turn_order}>
     */
    GameDatabase.prototype.getInitiativeOrder = function (scene_id) {
        return this.db.prepare("SELECT character_id, npc_id, actor_name, initiative_score, turn_order\n       FROM initiative_order\n       WHERE scene_id = ?\n       ORDER BY turn_order ASC, initiative_score DESC").all(scene_id);
    };
    // --- Combat Turn Management ---
    /**
     * Advances to the next actor's turn in the initiative order for a scene.
     * If at end of order, wraps to next round and resets turn order.
     */
    GameDatabase.prototype.advanceTurn = function (scene_id) {
        // Get scene state or initialize
        var sceneResult = this.db.prepare('SELECT * FROM scenes WHERE scene_id = ?').get(scene_id);
        var scene;
        if (!sceneResult) {
            // initialize at start of combat (turn_order 1, round 1)
            this.db.prepare('INSERT INTO scenes (scene_id, current_round, current_turn_order) VALUES (?, ?, ?)').run(scene_id, 1, 1);
            scene = { scene_id: scene_id, current_round: 1, current_turn_order: 1 };
        }
        else {
            scene = sceneResult;
        }
        // Get current order for this scene, sorted by turn_order ASC
        var order = this.db.prepare('SELECT * FROM initiative_order WHERE scene_id = ? ORDER BY turn_order ASC').all(scene_id);
        if (!order || order.length === 0) {
            return { success: false, message: "Initiative order not set for this scene." };
        }
        var currTurnOrder = scene.current_turn_order || 0;
        var currRound = scene.current_round || 1;
        var nextTurnOrder = currTurnOrder + 1;
        var nextRound = currRound;
        // wrap if needed
        if (nextTurnOrder > order.length) {
            nextTurnOrder = 1;
            nextRound += 1;
        }
        // Save state
        this.db.prepare('UPDATE scenes SET current_turn_order = ?, current_round = ? WHERE scene_id = ?').run(nextTurnOrder, nextRound, scene_id);
        var nextActor = order[nextTurnOrder - 1];
        return {
            success: true,
            next_actor: nextActor,
            new_round: nextRound,
            new_turn_order: nextTurnOrder
        };
    };
    /**
     * Returns the current actor whose turn it is, and round info.
     */
    GameDatabase.prototype.getCurrentTurn = function (scene_id) {
        var sceneResult = this.db.prepare('SELECT * FROM scenes WHERE scene_id = ?').get(scene_id);
        if (!sceneResult)
            return { success: false, message: "Scene not initialized. Call advanceTurn or set_initiative first." };
        var scene = sceneResult;
        var order = this.db.prepare('SELECT * FROM initiative_order WHERE scene_id = ? ORDER BY turn_order ASC').all(scene_id);
        if (!order || order.length === 0)
            return { success: false, message: "No initiative order set for this scene." };
        var currIndex = (scene.current_turn_order || 1) - 1;
        var actor = order[currIndex];
        return {
            success: true,
            current_actor: actor,
            current_round: scene.current_round,
            current_turn_order: scene.current_turn_order
        };
    };
    /**
     * Clear all initiative tracking for a scene.
     * @param scene_id The ID of the scene.
     */
    GameDatabase.prototype.clearInitiative = function (scene_id) {
        this.db.prepare("DELETE FROM initiative_order WHERE scene_id = ?").run(scene_id);
    };
    // ---- Status Effects Management (Public API) ----
    GameDatabase.prototype.addStatusEffect = function (opts) {
        var target_type = opts.target_type, target_id = opts.target_id, effect_name = opts.effect_name, _a = opts.description, description = _a === void 0 ? '' : _a, _b = opts.mechanical_effect, mechanical_effect = _b === void 0 ? {} : _b, _c = opts.duration_type, duration_type = _c === void 0 ? 'indefinite' : _c, _d = opts.duration_value, duration_value = _d === void 0 ? null : _d;
        var targetKey = target_type === 'character' ? 'character_id' : 'npc_id';
        var dbres = this.db.prepare("INSERT INTO status_effects (".concat(targetKey, ", effect_name, description, mechanical_effect, duration_type, duration_value)\n       VALUES (?, ?, ?, ?, ?, ?)")).run(target_id, effect_name, description, JSON.stringify(mechanical_effect !== null && mechanical_effect !== void 0 ? mechanical_effect : {}), duration_type, duration_value);
        return dbres.lastInsertRowid;
    };
    GameDatabase.prototype.removeStatusEffect = function (effect_id) {
        var stmt = this.db.prepare("DELETE FROM status_effects WHERE id = ?");
        var res = stmt.run(effect_id);
        return res.changes > 0;
    };
    GameDatabase.prototype.listStatusEffects = function (target_type, target_id) {
        var targetKey = target_type === 'character' ? 'character_id' : 'npc_id';
        var stmt = this.db.prepare("SELECT * FROM status_effects WHERE ".concat(targetKey, " = ? ORDER BY id ASC"));
        return stmt.all(target_id).map(function (e) { return (__assign(__assign({}, e), { mechanical_effect: e.mechanical_effect ? JSON.parse(e.mechanical_effect) : {} })); });
    };
    return GameDatabase;
}());
exports.GameDatabase = GameDatabase;
