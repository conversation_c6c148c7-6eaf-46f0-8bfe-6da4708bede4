// Utility: Serialize any array of strings/objects as { type: 'text', text: string }[] for MCP compliance
function makeTextContentArray(contentArr: any[]): { type: 'text', text: string }[] {
  return contentArr.map(entry => {
    if (typeof entry === "string") {
      return { type: 'text', text: entry };
    }
    if (entry && typeof entry === "object" && entry.type === "text" && typeof entry.text === "string") {
      // Already compliant
      return entry;
    }
    // For any other objects/values, serialize as prettified JSON
    return { type: 'text', text: JSON.stringify(entry, null, 2) };
  });
}
// File: game-state-server/src/index.ts

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { GameDatabase } from './db.js';
import { formatSheetByGameLine } from './characterSheets.js';
import { ANTAGONIST_TEMPLATES, AntagonistSheet } from './antagonists.js';
import type { NpcRow, AntagonistRow } from './db.js';

const db = new GameDatabase();
const server = new Server({ name: 'rpg-game-state-server', version: '2.1.0' }, { capabilities: { tools: {} } });

const toolDefinitions: any[] = [
  {
    name: 'create_character',
    description: 'Create a new oWoD character.',
    inputSchema: {
      type: 'object',
      properties: {
        // Core character properties
        name: { type: 'string', description: 'Character name' },
        concept: { type: 'string', description: 'Character concept', nullable: true },
        game_line: { type: 'string', enum: ['vampire', 'werewolf', 'mage', 'changeling'], description: 'Game line/splat' },
        // Attributes and basic traits are filled in backend with defaults or can be optionally included
        // --- Vampire-specific fields
        clan: { type: 'string', description: 'Vampire clan (e.g., Brujah, Malkavian)', nullable: true },
        generation: { type: 'number', description: 'Vampire generation', nullable: true },
        blood_pool_current: { type: 'number', description: 'Current Blood Pool', nullable: true },
        blood_pool_max: { type: 'number', description: 'Max Blood Pool', nullable: true },
        humanity: { type: 'number', description: 'Humanity (Vampire only)', nullable: true },
        // --- Werewolf-specific fields
        breed: { type: 'string', description: 'Werewolf breed (e.g., Homid, Metis, Lupus)', nullable: true },
        auspice: { type: 'string', description: 'Werewolf auspice (e.g., Ragabash, Theurge)', nullable: true },
        tribe: { type: 'string', description: 'Werewolf tribe', nullable: true },
        gnosis_current: { type: 'number', description: 'Current Gnosis', nullable: true },
        gnosis_permanent: { type: 'number', description: 'Permanent Gnosis', nullable: true },
        rage_current: { type: 'number', description: 'Current Rage', nullable: true },
        rage_permanent: { type: 'number', description: 'Permanent Rage', nullable: true },
        renown_glory: { type: 'number', description: 'Glory Renown', nullable: true },
        renown_honor: { type: 'number', description: 'Honor Renown', nullable: true },
        renown_wisdom: { type: 'number', description: 'Wisdom Renown', nullable: true },
        // --- Mage-specific fields
        tradition_convention: { type: 'string', description: 'Mage tradition or Convention', nullable: true },
        arete: { type: 'number', description: 'Mage Arete', nullable: true },
        quintessence: { type: 'number', description: 'Mage Quintessence', nullable: true },
        paradox: { type: 'number', description: 'Mage Paradox', nullable: true },
        // --- Changeling-specific fields
        kith: { type: 'string', description: 'Changeling kith', nullable: true },
        seeming: { type: 'string', description: 'Changeling seeming', nullable: true },
        glamour_current: { type: 'number', description: 'Current Glamour', nullable: true },
        glamour_permanent: { type: 'number', description: 'Permanent Glamour', nullable: true },
        banality_permanent: { type: 'number', description: 'Permanent Banality', nullable: true },

        // Optionally add abilities, disciplines, spheres, arts, realms, etc.
        abilities: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Starting abilities for the character' },
        disciplines: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Starting disciplines (Vampire only)' },
        spheres: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Spheres (Mage only)' },
        arts: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Changeling Arts' },
        realms: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Changeling Realms' }
      },
      required: ['name', 'game_line']
    }
  },
  {
    name: 'get_character',
    description: 'Retrieve full character data.',
    inputSchema: {
      type: 'object',
      properties: { character_id: { type: 'number' } },
      required: ['character_id']
    }
  },
  {
    name: 'get_character_by_name',
    description: 'Retrieve character by name.',
    inputSchema: {
      type: 'object',
      properties: { name: { type: 'string' } },
      required: ['name']
    }
  },
  {
    name: 'update_character',
    description: 'Update character traits.',
    inputSchema: {
      type: 'object',
      properties: {
        character_id: { type: 'number' },
        updates: { type: 'object' }
      },
      required: ['character_id', 'updates']
    }
  },
  {
    name: 'spend_resource',
    description: 'Spend a character resource.',
    inputSchema: {
      type: 'object',
      properties: {
        character_id: { type: 'number' },
        resource_name: { type: 'string', enum: ['willpower', 'blood', 'gnosis', 'rage', 'glamour', 'quintessence', 'paradox'] },
        amount: { type: 'number', default: 1 }
      },
      required: ['character_id', 'resource_name']
    }
  },
  {
    name: "restore_resource",
    description: "Restore a character resource like Willpower, Blood, etc.",
    inputSchema: {
      type: "object",
      properties: {
        character_id: { type: "number" },
        resource_name: { type: "string", enum: ['willpower', 'blood', 'gnosis', 'rage', 'glamour', 'quintessence'] },
        amount: { type: 'number', default: 1 }
      },
      required: ['character_id', 'resource_name']
    }
  },
  {
    name: 'apply_damage',
    description: 'Apply health level damage to a target after a successful damage roll.',
    inputSchema: {
      type: 'object',
      properties: {
        target_type: { type: 'string', enum: ['character', 'npc'] },
        target_id: { type: 'number' },
        damage_successes: { type: 'number', description: 'The number of successes from the damage roll.' },
        damage_type: { type: 'string', enum: ['bashing', 'lethal', 'aggravated'], default: 'lethal' }
      },
      required: ['target_type', 'target_id', 'damage_successes', 'damage_type']
    }
  },
  {
    name: 'create_antagonist',
    description: 'Create an antagonist from a template.',
    inputSchema: {
      type: 'object',
      properties: {
        template_name: { type: 'string' },
        custom_name: { type: 'string' }
      },
      required: ['template_name']
    }
  },
  {
    name: 'get_antagonist',
    description: 'Retrieve antagonist data by ID.',
    inputSchema: {
      type: 'object',
      properties: { npc_id: { type: 'number' } },
      required: ['npc_id']
    }
  },
{
  name: 'gain_resource',
  description: 'Gain a resource through an in-game action (e.g., feeding, meditation, quest). Applies game-line–specific logic.',
  inputSchema: {
    type: 'object',
    properties: {
      character_id: { type: 'number' },
      resource_name: { type: 'string', enum: ['willpower', 'blood', 'gnosis', 'glamour', 'quintessence'] },
      roll_successes: { type: 'number', minimum: 1 }
    },
    required: ['character_id', 'resource_name', 'roll_successes']
  }
}
];
// --- Initiative tool definitions ---
toolDefinitions.push(
  {
    name: "set_initiative",
    description: "Set the initiative order for a scene. Overwrites all entries for that scene. Each entry may be a PC or NPC.",
    inputSchema: {
      type: "object",
      properties: {
        scene_id: { type: "string" },
        entries: {
          type: "array",
          items: {
            type: "object",
            properties: {
              character_id: { type: ["number", "null"] },
              npc_id: { type: ["number", "null"] },
              actor_name: { type: "string" },
              initiative_score: { type: "number" },
              turn_order: { type: "number" }
            },
            required: ["actor_name", "initiative_score", "turn_order"]
          }
        }
      },
      required: ["scene_id", "entries"]
    }
  },
  {
    name: "get_initiative_order",
    description: "Get current initiative order for the specified scene.",
    inputSchema: {
      type: "object",
      properties: {
        scene_id: { type: "string" }
      },
      required: ["scene_id"]
    }
  }
);
  // Combat Turn Management:
toolDefinitions.push(
  {
    name: 'advance_turn',
    description: 'Advances to the next actor in the initiative order for a scene.',
    inputSchema: {
      type: 'object',
      properties: { scene_id: { type: 'string' } },
      required: ['scene_id']
    }
  },
  {
    name: 'get_current_turn',
    description: 'Retrieve the current actor and round info for a combat scene.',
    inputSchema: {
      type: 'object',
      properties: { scene_id: { type: 'string' } },
      required: ['scene_id']
    }
  }
);
// XP management tools:
toolDefinitions.push(
  {
    name: 'award_xp',
    description: 'Award experience points to a character.',
    inputSchema: {
      type: 'object',
      properties: {
        character_id: { type: 'number' },
        amount: { type: 'number', minimum: 1 },
        reason: { type: 'string' }
      },
      required: ['character_id', 'amount', 'reason']
    }
  },
  {
    name: 'spend_xp',
    description: 'Spend a character\'s XP to improve a trait (logging only; does not yet update trait).',
    inputSchema: {
      type: 'object',
      properties: {
        character_id: { type: 'number' },
        amount: { type: 'number', minimum: 1 },
        reason: { type: 'string' },
        trait_name: { type: 'string' },
        trait_info: { type: 'object' }
      },
      required: ['character_id', 'amount', 'reason']
    }
  }
);
// -- Add improve_trait tool schema
toolDefinitions.push({
  name: 'improve_trait',
  description: 'Increase a trait for a character by spending XP according to oWoD rules. Computes XP cost, checks XP, applies change and deduction. Supported trait_types: attribute, ability, discipline, sphere, art, realm, willpower, power_stat. trait_name must match the trait/facet name, e.g. "strength" or "Firearms".',
  inputSchema: {
    type: 'object',
    properties: {
      character_id: { type: 'number' },
      trait_type: {
        type: 'string',
        enum: ['attribute', 'ability', 'discipline', 'sphere', 'art', 'realm', 'willpower', 'power_stat']
      },
      trait_name: { type: 'string' }
    },
    required: ['character_id', 'trait_type', 'trait_name']
  }
});
/**
 * Calculates the XP cost to improve a character trait to the next level.
 */
toolDefinitions.push({
  name: 'get_trait_improvement_cost',
  description: 'Calculates the XP cost to improve a character trait to the next level.',
  inputSchema: {
    type: 'object',
    properties: {
      character_id: { type: 'number' },
      trait_type: {
        type: 'string',
        enum: ['attribute', 'ability', 'discipline', 'sphere', 'art', 'realm', 'willpower', 'power_stat']
      },
      trait_name: { type: 'string' }
    },
    required: ['character_id', 'trait_type', 'trait_name']
  }
});

async function rollInitiativeForScene(scene_id: string, actors: any[]) {
  const initiativeResults = actors.map(actor => {
    const initiativeScore = actor.initiative_pool;
    return {
      actor_name: actor.actor_name,
      character_id: actor.character_id,
      npc_id: actor.npc_id,
      initiative_score: initiativeScore,
      turn_order: 0 // Default value, will be sorted later
    };
  });

  // Sort by initiative score in descending order
  initiativeResults.sort((a, b) => b.initiative_score - a.initiative_score);

  // Assign turn order based on sorted initiative scores
  initiativeResults.forEach((result, index) => {
    result.turn_order = index + 1;
  });

  return initiativeResults;
}

export async function handleToolRequest(request: any) {
  const { name, arguments: args } = request.params;
  try {
    switch (name) {

    case 'create_character': {
      const char = db.createCharacter(args);
      if (!char) throw new Error("Character creation failed in database.");
      return {
        content: [
          { type: 'text', text: `Character ${char!.name} created with ID ${char!.id}` },
          { type: 'text', text: JSON.stringify({ character_id: char!.id, name: char!.name }) }
        ]
      };
    }

    // --- Inventory Management ---
    case 'add_item': {
      const { character_id, item } = args;
      const newItem = db.addItem(character_id, item);
      return { content: [{ type: 'text', text: `✅ Added '${newItem.name}' to character #${character_id}'s inventory.` }] };
    }
    case 'get_inventory': {
      const inventory = db.getInventory(args.character_id);
      const output = `🎒 Inventory for Character #${args.character_id}:\n` +
        (inventory.length > 0
          ? inventory.map((item: any) => `- ${item.item_name} (x${item.quantity}) [ID: ${item.id}]`).join('\n')
          : '  (Empty)');
      return { content: [{ type: 'text', text: output }] };
    }
    case 'update_item': {
      db.updateItem(args.item_id, args.updates);
      return { content: [{ type: 'text', text: `✅ Item #${args.item_id} updated.` }] };
    }
    case 'remove_item': {
      const success = db.removeItem(args.item_id);
      return { content: [{ type: 'text', text: success ? `🗑️ Item #${args.item_id} removed.` : '❌ Item not found.' }] };
    }

    // --- World & Story Persistence ---
    case 'save_world_state': {
      const { location, notes, data } = args;
      db.saveWorldState({ location, notes, data });
      return { content: [{ type: 'text', text: `🌍 World state saved successfully.` }] };
    }
    case 'get_world_state': {
      const state = db.getWorldState();
      return { content: [{ type: 'text', text: state ? JSON.stringify(state, null, 2) : 'No world state saved yet.' }] };
    }
    case 'save_story_progress': {
      const { chapter, scene, summary } = args;
      if (!chapter) {
        return { content: [{ type: 'text', text: `❌ Chapter is required.` }], isError: true };
      }
      db.saveStoryProgress({ chapter, scene, summary });
      return { content: [{ type: 'text', text: `📖 Story progress logged for Chapter ${chapter}.` }] };
    }
    case 'create_antagonist': {
  const { template_name, custom_name } = args;
  const antagonist = db.createAntagonist(template_name, custom_name) as AntagonistRow;
  if (!antagonist) throw new Error("Antagonist creation failed in database.");
  return {
    content: [
      { type: 'text', text: `Antagonist ${antagonist.name} created with ID ${antagonist.id}` },
      { type: 'text', text: JSON.stringify({ npc_id: antagonist.id, name: antagonist.name }) }
    ]
  };
    }
    // --- Antagonist & Character Management ---
    case 'update_antagonist': {
      db.updateAntagonist(args.npc_id, args.updates);
      return { content: [{ type: 'text', text: `✅ Antagonist #${args.npc_id} updated.` }] };
    }
    case 'list_antagonists': {
      const npcs = db.listAntagonists();
      const output = `👥 Antagonist Roster:\n` +
        (npcs.length > 0 ? npcs.map((npc: any) => `- ${npc.name} (${npc.game_line}) [ID: ${npc.id}]`).join('\n') : '  (None)');
      return { content: [{ type: 'text', text: output }] };
    }
    case 'remove_antagonist': {
      const success = db.removeAntagonist(args.npc_id);
      return { content: [{ type: 'text', text: success ? `🗑️ Antagonist #${args.npc_id} removed.` : '❌ Antagonist not found.' }] };
    }
    case 'list_characters': {
      const characters = db.listCharacters();
      const output = `🎭 Character Roster:\n` +
        (characters.length > 0 ? characters.map((char: any) => `- ${char.name} (${char.game_line}) [ID: ${char.id}]`).join('\n') : '  (None)');
      return { content: [{ type: 'text', text: output }] };
    }
    // ---- STATUS EFFECTS SYSTEM ----
    case 'apply_status_effect': {
      const { target_type, target_id, effect_name, description = '', mechanical_effect = {}, duration_type = 'indefinite', duration_value = null } = args;
      const effectId = db.addStatusEffect({
        target_type,
        target_id,
        effect_name,
        description,
        mechanical_effect,
        duration_type,
        duration_value,
      });
      return {
        content: [
          { type: 'text', text: `🌀 Status effect '${effect_name}' applied to ${target_type} #${target_id} (ID: ${effectId})` },
          { type: 'text', text: JSON.stringify({ effect_id: effectId, target_type, target_id, effect_name, duration_type, duration_value }) }
        ]
      };
    }

      case 'remove_status_effect': {
        const { effect_id } = args;
        const ok = db.removeStatusEffect(effect_id);
        if (!ok) {
          return { content: [{ type: 'text', text: `❌ Status effect ID ${effect_id} not found.` }], isError: true };
        }
        return { content: [{ type: 'text', text: `✅ Status effect ${effect_id} removed.` }] };
      }

      case 'get_status_effects': {
        const { target_type, target_id } = args;
        const effects = db.listStatusEffects(target_type, target_id);
        return {
          content: [
            { type: 'text', text: JSON.stringify({ target_type, target_id, effects }) }
          ]
        };
      }
      case "roll_virtue_check": {
        const { character_id, virtue_name, difficulty } = args;
        return {
          content: makeTextContentArray([
            {
              description:
                "Delegating to combat-engine-server. Please call roll_wod_pool there.",
              next_tool_call: {
                server: "rpg-combat-engine-server",
                tool_name: "roll_wod_pool",
                arguments: { character_id, virtue_name, difficulty, pool_size: 3 }
              }
            }
          ])
        };
      }
    } // end switch
  } // close try
  catch (e: any) {
    return { content: [{ type: 'text', text: `❌ Error: ${e.message || String(e)}` }], isError: true };
    } // end catch
}

server.setRequestHandler(CallToolRequestSchema, handleToolRequest as any);

const stdioTransport = new StdioServerTransport();

// Use start if listen does not exist in SDK:
if (typeof (server as any).start === "function") {
  (server as any).start(stdioTransport);
} // else fallback: do nothing or throw

