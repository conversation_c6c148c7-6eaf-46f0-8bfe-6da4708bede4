// File: game-state-server/src/db.ts

import Database from 'better-sqlite3';
import { existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import { ANTAGONIST_TEMPLATES } from './antagonists.js';
import { HealthTracker, DamageObject } from './health-tracker.js';

// --- Interface Definitions ---
interface CharacterRow {
  [key: string]: any; // Allow dynamic access for trait improvements, etc.
  id: number;
  name: string;
  concept?: string | null;
  game_line: string;
  strength: number; dexterity: number; stamina: number;
  charisma: number; manipulation: number; appearance: number;
  perception: number; intelligence: number; wits: number;
  willpower_current: number; willpower_permanent: number;
  health_levels: string; // JSON
  experience: number;
  blood_pool_current?: number;
  // ... and all other game-line specific fields
}

export interface AntagonistRow {
  id: number;
  name: string;
  template: string;
  concept: string;
  game_line: string;
  strength: number;
  dexterity: number;
  stamina: number;
  charisma: number;
  manipulation: number;
  appearance: number;
  perception: number;
  intelligence: number;
  wits: number;
  willpower_current: number;
  willpower_permanent: number;
  health_levels: string;
  blood_pool_current: number;
  notes: string;
}

export interface NpcRow {
  id: number;
  name: string;
  template: string;
  concept: string;
  game_line: string;
  strength: number;
  dexterity: number;
  stamina: number;
  charisma: number;
  manipulation: number;
  appearance: number;
  perception: number;
  intelligence: number;
  wits: number;
  willpower_current: number;
  willpower_permanent: number;
  health_levels: string;
  blood_pool_current: number;
  notes: string;
}

// Create data directory in user's home folder
const DATA_DIR = join(homedir(), '.rpg-dungeon-data');
if (!existsSync(DATA_DIR)) {
  mkdirSync(DATA_DIR, { recursive: true });
}
const DB_PATH = join(DATA_DIR, 'game-state.db');

export class GameDatabase {
  private db: Database.Database;

  constructor() {
    this.db = new Database(DB_PATH);
    this.db.pragma('journal_mode = WAL');
    this.initializeSchema();
  }

  private initializeSchema() {
    // --- oWoD-centric Core Character Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS characters (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        concept TEXT,
        game_line TEXT NOT NULL,
        -- Core Attributes
        strength INTEGER DEFAULT 1, dexterity INTEGER DEFAULT 1, stamina INTEGER DEFAULT 1,
        charisma INTEGER DEFAULT 1, manipulation INTEGER DEFAULT 1, appearance INTEGER DEFAULT 1,
        perception INTEGER DEFAULT 1, intelligence INTEGER DEFAULT 1, wits INTEGER DEFAULT 1,
        -- Core Traits
        willpower_current INTEGER DEFAULT 1,
        willpower_permanent INTEGER DEFAULT 1,
        health_levels TEXT NOT NULL, -- JSON
blood_pool_current INTEGER DEFAULT 0
        experience INTEGER DEFAULT 0,
        blood_pool_current INTEGER DEFAULT 0
      );
    `);

    // --- Migration: Add 'experience' column if missing ---
    const cols = this.db.prepare("PRAGMA table_info(characters)").all() as Array<{ name: string }>;
    if (!cols.some(c => c.name === "experience")) {
      this.db.exec(`ALTER TABLE characters ADD COLUMN experience INTEGER DEFAULT 0`);
    }
	// --- Migration: Add 'blood_pool_current' column if missing ---
    const cols2 = this.db.prepare("PRAGMA table_info(characters)").all() as Array<{ name: string }>;
    if (!cols2.some(c => c.name === "blood_pool_current")) {
      this.db.exec(`ALTER TABLE characters ADD COLUMN blood_pool_current INTEGER DEFAULT 0`);
    }

    // --- Relational Tables ---
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_abilities (character_id INTEGER, ability_name TEXT, ability_type TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, ability_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_disciplines (character_id INTEGER, discipline_name TEXT, rating INTEGER, PRIMARY KEY(character_id, discipline_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_arts (character_id INTEGER, art_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, art_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_realms (character_id INTEGER, realm_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, realm_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_gifts (character_id INTEGER, gift_name TEXT, rank INTEGER, PRIMARY KEY(character_id, gift_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_spheres (character_id INTEGER, sphere_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, sphere_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_derangements (id INTEGER PRIMARY KEY, character_id INTEGER, derangement TEXT, description TEXT, UNIQUE(character_id, derangement), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS xp_ledger (id INTEGER PRIMARY KEY, character_id INTEGER, type TEXT, amount INTEGER, reason TEXT, trait TEXT, before_xp INTEGER, after_xp INTEGER, created_at DATETIME DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);

    // --- Inventory Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        character_id INTEGER NOT NULL,
        item_name TEXT NOT NULL,
        item_type TEXT, -- e.g., 'Weapon', 'Trinket', 'Consumable'
        quantity INTEGER DEFAULT 1,
        description TEXT,
        properties TEXT, -- JSON for stats like weapon damage, etc.
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );
    `);

    // --- World & Story Persistence Tables ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS world_state (
        id INTEGER PRIMARY KEY, -- Use a single row for the whole campaign for simplicity
        location TEXT,
        notes TEXT,
        data TEXT, -- Flexible JSON blob for NPCs, events, etc.
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    this.db.exec(`
      CREATE TABLE IF NOT EXISTS story_progress (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chapter INTEGER,
        scene TEXT,
        summary TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // --- Game-line Specific Trait Tables (modular) ---
    // Vampire
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_vampire_traits (
        character_id INTEGER PRIMARY KEY,
        clan TEXT,
        generation INTEGER,
        blood_pool_current INTEGER,
        blood_pool_max INTEGER,
        humanity INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);
    // Werewolf
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_werewolf_traits (
        character_id INTEGER PRIMARY KEY,
        breed TEXT,
        auspice TEXT,
        tribe TEXT,
        gnosis_current INTEGER,
        gnosis_permanent INTEGER,
        rage_current INTEGER,
        rage_permanent INTEGER,
        renown_glory INTEGER,
        renown_honor INTEGER,
        renown_wisdom INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);
    // Mage
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_mage_traits (
        character_id INTEGER PRIMARY KEY,
        tradition_convention TEXT,
        arete INTEGER,
        quintessence INTEGER,
        paradox INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);
    // Changeling
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_changeling_traits (
        character_id INTEGER PRIMARY KEY,
        kith TEXT,
        seeming TEXT,
        glamour_current INTEGER,
        glamour_permanent INTEGER,
        banality_permanent INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);

    // ADDITION: Experience Ledger table for character XP transactions
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS experience_ledger (
        id INTEGER PRIMARY KEY,
        character_id INTEGER NOT NULL,
        amount INTEGER NOT NULL,
        reason TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );
    `);

    // --- Refactored Modular Antagonists/NPCs Table ---
    this.db.exec(`DROP TABLE IF EXISTS npcs;`); // Backup data first!
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npcs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        template TEXT,
blood_pool_current INTEGER DEFAULT 0,
blood_pool_current INTEGER DEFAULT 0,
        concept TEXT,
        game_line TEXT NOT NULL,
        strength INTEGER DEFAULT 1, dexterity INTEGER DEFAULT 1, stamina INTEGER DEFAULT 1,
        charisma INTEGER DEFAULT 1, manipulation INTEGER DEFAULT 1, appearance INTEGER DEFAULT 1,
        perception INTEGER DEFAULT 1, intelligence INTEGER DEFAULT 1, wits INTEGER DEFAULT 1,
        willpower_current INTEGER DEFAULT 1,
        willpower_permanent INTEGER DEFAULT 1,
        health_levels TEXT NOT NULL, -- JSON
        blood_pool_current INTEGER DEFAULT 0,
        notes TEXT
      );
    `);
    // Modular splat trait tables for NPCs -- structure mirrors player traits
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_vampire_traits (
        npc_id INTEGER PRIMARY KEY,
        clan TEXT,
        generation INTEGER,
        blood_pool_current INTEGER,
        blood_pool_max INTEGER,
        humanity INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_werewolf_traits (
        npc_id INTEGER PRIMARY KEY,
        breed TEXT,
        auspice TEXT,
        tribe TEXT,
        gnosis_current INTEGER,
        gnosis_permanent INTEGER,
        rage_current INTEGER,
        rage_permanent INTEGER,
        renown_glory INTEGER,
        renown_honor INTEGER,
        renown_wisdom INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_mage_traits (
        npc_id INTEGER PRIMARY KEY,
        tradition_convention TEXT,
        arete INTEGER,
        quintessence INTEGER,
        paradox INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_changeling_traits (
        npc_id INTEGER PRIMARY KEY,
        kith TEXT,
        seeming TEXT,
        glamour_current INTEGER,
        glamour_permanent INTEGER,
        banality_permanent INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);

    // --- Initiative Tracking Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS initiative_order (
        scene_id TEXT NOT NULL,
        character_id INTEGER,
        npc_id INTEGER,
        actor_name TEXT NOT NULL,
        initiative_score INTEGER NOT NULL,
        turn_order INTEGER NOT NULL,
        PRIMARY KEY(scene_id, turn_order),
        FOREIGN KEY(character_id) REFERENCES characters(id) ON DELETE SET NULL,
        FOREIGN KEY(npc_id) REFERENCES npcs(id) ON DELETE SET NULL
      );
    `);
      // --- Turn Management Table for Combat Scenes ---
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS scenes (
          scene_id TEXT PRIMARY KEY,
          current_round INTEGER NOT NULL DEFAULT 1,
          current_turn_order INTEGER NOT NULL DEFAULT 0
        );
      `);
    // --- Generic Status Effects Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS status_effects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        character_id INTEGER,
        npc_id INTEGER,
        effect_name TEXT NOT NULL,
        description TEXT,
        mechanical_effect TEXT,
        duration_type TEXT DEFAULT 'indefinite',
        duration_value INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );
    `);
  }

  createCharacter(data: any) {
    if (!['vampire', 'werewolf', 'mage', 'changeling'].includes(data.game_line)) {
      throw new Error(`Invalid game_line: ${data.game_line}. Must be one of: vampire, werewolf, mage, changeling`);
    }

    const health_levels = data.health_levels || { bruised: 0, hurt: 0, injured: 0, wounded: 0, mauled: 0, crippled: 0, incapacitated: 0 };
    let charId: number | undefined = undefined;

    // Transactional logic: all sub-table inserts are done atomically
    charId = this.db.transaction(() => {
      let localCharId: number;
      // Insert core character data
      const stmt = this.db.prepare(`
        INSERT INTO characters (
          name, concept, game_line,
          strength, dexterity, stamina, charisma, manipulation, appearance,
          perception, intelligence, wits,
          willpower_current, willpower_permanent, health_levels, experience, blood_pool_current
        ) VALUES (
          @name, @concept, @game_line,
          @strength, @dexterity, @stamina, @charisma, @manipulation, @appearance,
          @perception, @intelligence, @wits,
          @willpower_current, @willpower_permanent, @health_levels, @experience, @blood_pool_current
        )
      `);

      const result = stmt.run({
        name: data.name,
        concept: data.concept || null,
        game_line: data.game_line,
        strength: data.strength || 1,
        dexterity: data.dexterity || 1,
        stamina: data.stamina || 1,
        charisma: data.charisma || 1,
        manipulation: data.manipulation || 1,
        appearance: data.appearance || 1,
        perception: data.perception || 1,
        intelligence: data.intelligence || 1,
        wits: data.wits || 1,
        willpower_current: data.willpower_current || 1,
        willpower_permanent: data.willpower_permanent || 1,
        health_levels: JSON.stringify(health_levels),
        experience: data.experience || 0,
        blood_pool_current: data.blood_pool_current || 0
      });
      localCharId = result.lastInsertRowid as number;

      // --- Insert into game-line-specific tables ---

      switch (data.game_line) {
        case 'vampire':
          this.db.prepare(`
            INSERT INTO character_vampire_traits
            (character_id, clan, generation, blood_pool_current, blood_pool_max, humanity)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.clan ?? null, data.generation ?? null,
            data.blood_pool_current ?? null, data.blood_pool_max ?? null,
            data.humanity ?? null
          );
          break;
        case 'werewolf':
          this.db.prepare(`
            INSERT INTO character_werewolf_traits
            (character_id, breed, auspice, tribe, gnosis_current, gnosis_permanent, rage_current, rage_permanent, renown_glory, renown_honor, renown_wisdom)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.breed ?? null, data.auspice ?? null, data.tribe ?? null,
            data.gnosis_current ?? null, data.gnosis_permanent ?? null,
            data.rage_current ?? null, data.rage_permanent ?? null,
            data.renown_glory ?? null, data.renown_honor ?? null, data.renown_wisdom ?? null
          );
          break;
        case 'mage':
          this.db.prepare(`
            INSERT INTO character_mage_traits
            (character_id, tradition_convention, arete, quintessence, paradox)
            VALUES (?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.tradition_convention ?? null,
            data.arete ?? null,
            data.quintessence ?? null,
            data.paradox ?? null
          );
          break;
        case 'changeling':
          this.db.prepare(`
            INSERT INTO character_changeling_traits
            (character_id, kith, seeming, glamour_current, glamour_permanent, banality_permanent)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.kith ?? null, data.seeming ?? null,
            data.glamour_current ?? null, data.glamour_permanent ?? null,
            data.banality_permanent ?? null
          );
          break;
        // Additional splats can be added here in similar fashion
      }

      // Changeling-specific: arts/reals
      if (data.game_line === "changeling") {
        if (data.arts && Array.isArray(data.arts)) {
          const artStmt = this.db.prepare(
            `INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)`
          );
          for (const a of data.arts) {
            artStmt.run(localCharId, a.art_name ?? a.name ?? a.label ?? '', Number(a.rating) || 0);
          }
        }
        if (data.realms && Array.isArray(data.realms)) {
          const realmStmt = this.db.prepare(
            `INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)`
          );
          for (const r of data.realms) {
            realmStmt.run(localCharId, r.realm_name ?? r.name ?? r.label ?? '', Number(r.rating) || 0);
          }
        }
      }

      // Example sub-table transactional inserts; expand for all relations as needed
      if (data.abilities && Array.isArray(data.abilities)) {
        const abilityStmt = this.db.prepare(
          `INSERT INTO character_abilities (character_id, ability_name, ability_type, rating, specialty)
           VALUES (?, ?, ?, ?, ?)`
        );
        for (const ability of data.abilities) {
          abilityStmt.run(localCharId, ability.name, ability.type, ability.rating, ability.specialty ?? null);
        }
      }
      if (data.disciplines && Array.isArray(data.disciplines)) {
        const discStmt = this.db.prepare(
          `INSERT INTO character_disciplines (character_id, discipline_name, rating)
           VALUES (?, ?, ?)`
        );
        for (const d of data.disciplines) {
          discStmt.run(localCharId, d.name, d.rating);
        }
      }
      // ... perform additional transactional inserts for arts, realms, gifts, etc., as needed
      return localCharId;
    })();

    return this.getCharacter(charId!);
  }
    
  createAntagonist(template_name: string, custom_name?: string) {
    const template = (ANTAGONIST_TEMPLATES as any)[template_name];
    if (!template) return null;
    // Fill missing health_levels from default if template omits it
    const defaultHealthLevels = { bruised: 0, hurt: 0, injured: 0, wounded: 0, mauled: 0, crippled: 0, incapacitated: 0 };
    const data = {
      ...template,
      name: custom_name || template.name || template_name,
      template: template_name,
      health_levels: template.health_levels ?? defaultHealthLevels
    };
    let npcId: number | undefined = undefined;

    // Validate required fields after filling health_levels
    if (!data.name || !data.game_line || !data.health_levels) {
      console.error("Missing required fields in antagonist template:", template_name, data);
      return null;
    }

    
        this.db.transaction(() => {
          // 1. Insert into new lean core npcs table (no game-line-specific splat traits here)
          const stmt = this.db.prepare(`
            INSERT INTO npcs (
              name, template, concept, game_line,
              strength, dexterity, stamina, charisma, manipulation, appearance,
              perception, intelligence, wits,
              willpower_current, willpower_permanent, health_levels, blood_pool_current, notes
            ) VALUES (
              @name, @template, @concept, @game_line,
              @strength, @dexterity, @stamina, @charisma, @manipulation, @appearance,
              @perception, @intelligence, @wits,
              @willpower_current, @willpower_permanent, @health_levels, @blood_pool_current, @notes
            )
          `);
          const result = stmt.run({
            name: data.name,
    		template: data.template,
            concept: data.concept || null,
            game_line: data.game_line,
            strength: data.strength || 1,
    		dexterity: data.dexterity || 1,
    		stamina: data.stamina || 1,
    		charisma: data.charisma || 1,
    		manipulation: data.manipulation || 1,
    		appearance: data.appearance || 1,
            perception: data.perception || 1,
    		intelligence: data.intelligence || 1,
    		wits: data.wits || 1,
    		willpower_current: data.willpower_current || 1,
    		willpower_permanent: data.willpower_permanent || 1,
            health_levels: JSON.stringify(data.health_levels ?? {}),
            blood_pool_current: data.blood_pool_current || 0,
    		notes: data.notes || null
          });
          npcId = result.lastInsertRowid as number;
      // 2. Populate game-line-specific traits in new modular tables
      switch (template.game_line) {
        case 'vampire':
          this.db.prepare(`
            INSERT INTO npc_vampire_traits
            (npc_id, clan, generation, blood_pool_current, blood_pool_max, humanity)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.clan ?? null,
            template.generation ?? null,
            template.blood_pool_current ?? null,
            template.blood_pool_max ?? null,
            template.humanity ?? null
          );
          break;
        case 'werewolf':
          this.db.prepare(`
            INSERT INTO npc_werewolf_traits
            (npc_id, breed, auspice, tribe, gnosis_current, gnosis_permanent, rage_current, rage_permanent, renown_glory, renown_honor, renown_wisdom)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.breed ?? null,
            template.auspice ?? null,
            template.tribe ?? null,
            template.gnosis_current ?? null,
            template.gnosis_permanent ?? null,
            template.rage_current ?? null,
            template.rage_permanent ?? null,
            template.renown_glory ?? null,
            template.renown_honor ?? null,
            template.renown_wisdom ?? null
          );
          break;
        case 'mage':
          this.db.prepare(`
            INSERT INTO npc_mage_traits
            (npc_id, tradition_convention, arete, quintessence, paradox)
            VALUES (?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.tradition_convention ?? null,
            template.arete ?? null,
            template.quintessence ?? null,
            template.paradox ?? null
          );
          break;
        case 'changeling':
          this.db.prepare(`
            INSERT INTO npc_changeling_traits
            (npc_id, kith, seeming, glamour_current, glamour_permanent, banality_permanent)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.kith ?? null,
            template.seeming ?? null,
            template.glamour_current ?? null,
            template.glamour_permanent ?? null,
            template.banality_permanent ?? null
          );
          break;
        // Expand for other splats as needed
      }

      // 3. Relational data (abilities, disciplines, gifts, spheres, arts, realms)
      if (template.abilities) {
        const abilities = template.abilities;
        const abilityStmt = this.db.prepare(`INSERT INTO character_abilities (character_id, ability_name, ability_type, rating, specialty) VALUES (?, ?, ?, ?, NULL)`);
        if (abilities.talents) {
          for (const [name, rating] of Object.entries(abilities.talents)) {
            abilityStmt.run(npcId, name, 'Talent', rating);
          }
        }
        if (abilities.skills) {
          for (const [name, rating] of Object.entries(abilities.skills)) {
            abilityStmt.run(npcId, name, 'Skill', rating);
          }
        }
        if (abilities.knowledges) {
          for (const [name, rating] of Object.entries(abilities.knowledges)) {
            abilityStmt.run(npcId, name, 'Knowledge', rating);
          }
        }
      }
      if (template.supernatural?.disciplines) {
        const discStmt = this.db.prepare(`INSERT INTO character_disciplines (character_id, discipline_name, rating) VALUES (?, ?, ?)`);
        for (const [name, rating] of Object.entries(template.supernatural.disciplines)) {
          discStmt.run(npcId, name, rating);
        }
      }
      if (template.supernatural?.gifts) {
        const giftStmt = this.db.prepare(`INSERT INTO character_gifts (character_id, gift_name, rank) VALUES (?, ?, ?)`);
        for (const [name, rank] of Object.entries(template.supernatural.gifts)) {
          giftStmt.run(npcId, name, rank);
        }
      }
      if (template.supernatural?.spheres) {
        const sphStmt = this.db.prepare(`INSERT INTO character_spheres (character_id, sphere_name, rating) VALUES (?, ?, ?)`);
        for (const [name, rating] of Object.entries(template.supernatural.spheres)) {
          sphStmt.run(npcId, name, rating);
        }
      }
      if (template.supernatural?.arts) {
        const artStmt = this.db.prepare(`INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)`);
        for (const [name, rating] of Object.entries(template.supernatural.arts)) {
          artStmt.run(npcId, name, rating);
        }
      }
      if (template.supernatural?.realms) {
        const realmStmt = this.db.prepare(`INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)`);
        for (const [name, rating] of Object.entries(template.supernatural.realms)) {
          realmStmt.run(npcId, name, rating);
        }
      }
      // ... Add more relational logic for new splats here if needed
    })();

    return this.getAntagonistById(npcId!);
  }

  getAntagonistById(npc_id: number) {
    return this.db.prepare('SELECT * FROM npcs WHERE id = ?').get(npc_id);
  }

  getCharacter(id: number): (CharacterRow & {
    abilities: any[];
    disciplines: any[];
  // Possibly add other sub-tables here if fetched later (arts, realms, spheres, etc).
  [key: string]: any;
 }) | null {
   const char = this.db.prepare('SELECT * FROM characters WHERE id = ?').get(id) as CharacterRow | undefined;
   if (!char) return null;
   
   let extraData: Record<string, any> = {};
   switch (char.game_line) {
     case 'vampire':
       extraData = this.db.prepare('SELECT * FROM character_vampire_traits WHERE character_id = ?').get(id) || {};
       break;
     case 'werewolf':
       extraData = this.db.prepare('SELECT * FROM character_werewolf_traits WHERE character_id = ?').get(id) || {};
       break;
     case 'mage':
       extraData = this.db.prepare('SELECT * FROM character_mage_traits WHERE character_id = ?').get(id) || {};
       break;
     case 'changeling':
       extraData = this.db.prepare('SELECT * FROM character_changeling_traits WHERE character_id = ?').get(id) || {};
       break;
      // Add more splats as needed
      default:
        break;
    }

    // Fetch abilities, disciplines, spheres, gifts, arts, and realms
    const abilities = this.db.prepare('SELECT * FROM character_abilities WHERE character_id = ?').all(id);
    const disciplines = this.db.prepare('SELECT * FROM character_disciplines WHERE character_id = ?').all(id);
    const spheres = this.db.prepare('SELECT * FROM character_spheres WHERE character_id = ?').all(id);
    const gifts = this.db.prepare('SELECT * FROM character_gifts WHERE character_id = ?').all(id);
    const arts = this.db.prepare('SELECT * FROM character_arts WHERE character_id = ?').all(id);
    const realms = this.db.prepare('SELECT * FROM character_realms WHERE character_id = ?').all(id);

    return {
      ...char,
      ...extraData,
      abilities,
      disciplines,
      spheres,
      gifts,
      arts,
      realms,
    };
  }

  // --- STUBS FOR MCP CONTRACT COMPATIBILITY ---
  addItem(character_id: number, item: any): any {
    if (!character_id || !item?.name) {
      throw new Error("Invalid arguments: character_id and item.name are required");
    }
    const stmt = this.db.prepare(`
      INSERT INTO inventory (character_id, item_name, item_type, quantity, description, properties)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    const result = stmt.run(
      character_id,
      item.name,
      item.type || null,
      item.quantity || 1,
      item.description || null,
      item.properties ? JSON.stringify(item.properties) : null
    );
    return {
      id: result.lastInsertRowid,
      character_id,
      name: item.name,
      type: item.type || null,
      quantity: item.quantity || 1,
      description: item.description || null,
      properties: item.properties || null
    };
  }

  getInventory(character_id: number): any[] {
    return [];
  }

  updateItem(item_id: number, updates: any): void {
    // stub
  }

  removeItem(item_id: number): boolean {
    return true;
  }

  saveWorldState(state: any): void {
    // stub
  }

  getWorldState(): any {
    return {};
  }

  saveStoryProgress(progress: any): void {
    // stub
  }

  updateAntagonist(npc_id: number, updates: any): void {
    // stub
  }

  listAntagonists(): any[] {
    return this.db.prepare('SELECT * FROM npcs').all();
  }

  removeAntagonist(npc_id: number): boolean {
    return true;
  }

  listCharacters(): any[] {
    return [];
  }

  addStatusEffect(effect: any): number {
    if ((!effect.character_id && !effect.npc_id) || !effect.effect_name) {
      throw new Error("Invalid arguments: must provide (character_id or npc_id) and effect_name");
    }
    const stmt = this.db.prepare(`
      INSERT INTO status_effects (
        character_id, npc_id, effect_name, description, mechanical_effect, duration_type, duration_value
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    const result = stmt.run(
      effect.character_id || null,
      effect.npc_id || null,
      effect.effect_name,
      effect.description || null,
      effect.mechanical_effect || null,
      effect.duration_type || "indefinite",
      effect.duration_value || null
    );
    return result.lastInsertRowid as number;
  }

  removeStatusEffect(effect_id: number): boolean {
    return true;
  }

  listStatusEffects(target_type: string, target_id: number): any[] {
    if (!target_type || !target_id) return [];
    const col = target_type === "character"
      ? "character_id"
      : target_type === "npc"
      ? "npc_id"
      : null;
    if (!col) return [];
    return this.db.prepare(
      `SELECT * FROM status_effects WHERE ${col} = ?`
    ).all(target_id);
  }

  getCharacterByName(name: string): CharacterRow | null {
    const row = this.db.prepare('SELECT * FROM characters WHERE name = ?').get(name);
    return row ? (row as CharacterRow) : null;
  }

  getAntagonistByName(name: string): AntagonistRow | null {
    const row = this.db.prepare('SELECT * FROM npcs WHERE name = ?').get(name);
    return row ? (row as AntagonistRow) : null;
  }

  updateCharacter(character_id: number, updates: any): void {
    // stub
  }

}
